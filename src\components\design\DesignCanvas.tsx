import React, { useRef, useState, useCallback } from 'react';
import { Product, DesignElement } from '../../types';

interface DesignCanvasProps {
  product: Product;
  elements: DesignElement[];
  selectedElement: string | null;
  onSelectElement: (elementId: string | null) => void;
  onUpdateElement: (elementId: string, updates: Partial<DesignElement>) => void;
  zoom: number;
  isPreviewMode: boolean;
}

const DesignCanvas: React.FC<DesignCanvasProps> = ({
  product,
  elements,
  selectedElement,
  onSelectElement,
  onUpdateElement,
  zoom,
  isPreviewMode,
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [dragElement, setDragElement] = useState<string | null>(null);

  const printArea = product.printAreas[0]; // Use first print area

  const handleMouseDown = useCallback((e: React.MouseEvent, elementId: string) => {
    if (isPreviewMode) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    setIsDragging(true);
    setDragElement(elementId);
    setDragStart({ x: e.clientX, y: e.clientY });
    onSelectElement(elementId);
  }, [isPreviewMode, onSelectElement]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !dragElement) return;

    const deltaX = (e.clientX - dragStart.x) / zoom;
    const deltaY = (e.clientY - dragStart.y) / zoom;

    const element = elements.find(el => el.id === dragElement);
    if (!element) return;

    const newX = Math.max(0, Math.min(printArea.width - element.size.width, element.position.x + deltaX));
    const newY = Math.max(0, Math.min(printArea.height - element.size.height, element.position.y + deltaY));

    onUpdateElement(dragElement, {
      position: { x: newX, y: newY }
    });

    setDragStart({ x: e.clientX, y: e.clientY });
  }, [isDragging, dragElement, dragStart, zoom, elements, printArea, onUpdateElement]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragElement(null);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const handleCanvasClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onSelectElement(null);
    }
  };

  return (
    <div className="relative">
      <div
        ref={canvasRef}
        className="relative bg-white rounded-lg shadow-lg overflow-hidden"
        style={{
          width: 500 * zoom,
          height: 600 * zoom,
          transform: `scale(${zoom})`,
          transformOrigin: 'center center',
        }}
        onClick={handleCanvasClick}
      >
        {/* Product Background */}
        <img
          src={product.images[0]?.url}
          alt={product.name}
          className="w-full h-full object-cover pointer-events-none"
          draggable={false}
        />

        {/* Print Area Overlay */}
        {!isPreviewMode && (
          <div
            className="absolute border-2 border-dashed border-blue-400 bg-blue-50 bg-opacity-20"
            style={{
              left: printArea.x,
              top: printArea.y,
              width: printArea.width,
              height: printArea.height,
            }}
          >
            <div className="absolute top-2 left-2 text-xs text-blue-600 font-medium">
              Print Area
            </div>
          </div>
        )}

        {/* Design Elements */}
        {elements.map((element) => (
          <DesignElementRenderer
            key={element.id}
            element={element}
            isSelected={selectedElement === element.id}
            isPreviewMode={isPreviewMode}
            printArea={printArea}
            onMouseDown={(e) => handleMouseDown(e, element.id)}
          />
        ))}
      </div>
    </div>
  );
};

interface DesignElementRendererProps {
  element: DesignElement;
  isSelected: boolean;
  isPreviewMode: boolean;
  printArea: { x: number; y: number; width: number; height: number };
  onMouseDown: (e: React.MouseEvent) => void;
}

const DesignElementRenderer: React.FC<DesignElementRendererProps> = ({
  element,
  isSelected,
  isPreviewMode,
  printArea,
  onMouseDown,
}) => {
  const elementStyle: React.CSSProperties = {
    position: 'absolute',
    left: printArea.x + element.position.x,
    top: printArea.y + element.position.y,
    width: element.size.width,
    height: element.size.height,
    transform: `rotate(${element.rotation}deg)`,
    opacity: element.opacity,
    zIndex: element.zIndex,
    cursor: isPreviewMode ? 'default' : 'move',
    userSelect: 'none',
  };

  const renderContent = () => {
    switch (element.type) {
      case 'image':
        return (
          <img
            src={element.content}
            alt="Design element"
            className="w-full h-full object-contain"
            draggable={false}
          />
        );
      
      case 'text':
        return (
          <div
            className="w-full h-full flex items-center justify-center"
            style={{
              color: element.style?.color || '#000000',
              fontSize: element.style?.fontSize || 16,
              fontFamily: element.style?.fontFamily || 'Arial',
              fontWeight: element.style?.fontWeight || 'normal',
              textAlign: element.style?.textAlign || 'center',
              backgroundColor: element.style?.backgroundColor || 'transparent',
              borderColor: element.style?.borderColor,
              borderWidth: element.style?.borderWidth || 0,
              borderRadius: element.style?.borderRadius || 0,
            }}
          >
            {element.content}
          </div>
        );
      
      case 'shape':
        return (
          <div
            className="w-full h-full"
            style={{
              backgroundColor: element.style?.backgroundColor || '#000000',
              borderColor: element.style?.borderColor,
              borderWidth: element.style?.borderWidth || 0,
              borderRadius: element.style?.borderRadius || 0,
            }}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div
      style={elementStyle}
      onMouseDown={onMouseDown}
      className={`${isSelected && !isPreviewMode ? 'ring-2 ring-blue-500 ring-offset-2' : ''}`}
    >
      {renderContent()}
      
      {/* Resize Handles */}
      {isSelected && !isPreviewMode && (
        <>
          <div className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 border border-white rounded-full cursor-nw-resize" />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 border border-white rounded-full cursor-ne-resize" />
          <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 border border-white rounded-full cursor-sw-resize" />
          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 border border-white rounded-full cursor-se-resize" />
        </>
      )}
    </div>
  );
};

export default DesignCanvas;
