import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { Filter, Grid, List, Search, SlidersHorizontal, Heart, ShoppingCart } from 'lucide-react';

import { Product, ProductFilters } from '../../types';
import { useCart } from '../../contexts/CartContext';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Input from '../../components/ui/Input';
import LoadingSpinner from '../../components/ui/LoadingSpinner';

const ProductCatalogPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  const { addItem } = useCart();

  const [filters, setFilters] = useState<ProductFilters>({
    category: searchParams.get('category') || undefined,
    minPrice: searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined,
    maxPrice: searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined,
    sortBy: (searchParams.get('sortBy') as any) || 'popular',
  });

  // Mock products data
  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'Classic Cotton T-Shirt',
      description: 'Comfortable 100% cotton t-shirt perfect for custom designs',
      category: { id: '1', name: 'T-Shirts', slug: 't-shirts' },
      basePrice: 19.99,
      images: [
        {
          id: '1',
          url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=500&fit=crop',
          alt: 'Classic Cotton T-Shirt',
          isPrimary: true,
          sortOrder: 1,
        },
      ],
      variants: [
        { id: '1', name: 'Size', type: 'size', value: 'S', priceModifier: 0, sku: 'TSHIRT-S', stock: 100, isAvailable: true },
        { id: '2', name: 'Size', type: 'size', value: 'M', priceModifier: 0, sku: 'TSHIRT-M', stock: 100, isAvailable: true },
        { id: '3', name: 'Size', type: 'size', value: 'L', priceModifier: 2, sku: 'TSHIRT-L', stock: 100, isAvailable: true },
        { id: '4', name: 'Color', type: 'color', value: 'White', priceModifier: 0, sku: 'TSHIRT-WHITE', stock: 100, isAvailable: true },
        { id: '5', name: 'Color', type: 'color', value: 'Black', priceModifier: 0, sku: 'TSHIRT-BLACK', stock: 100, isAvailable: true },
      ],
      printAreas: [
        { id: '1', name: 'Front', x: 150, y: 100, width: 200, height: 300, maxDpi: 300, allowedFileTypes: ['png', 'jpg', 'svg'] },
      ],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tags: ['cotton', 'classic', 'comfortable'],
    },
    {
      id: '2',
      name: 'Ceramic Coffee Mug',
      description: 'High-quality ceramic mug perfect for your morning coffee',
      category: { id: '2', name: 'Mugs', slug: 'mugs' },
      basePrice: 14.99,
      images: [
        {
          id: '2',
          url: 'https://images.unsplash.com/photo-1514228742587-6b1558fcf93a?w=500&h=500&fit=crop',
          alt: 'Ceramic Coffee Mug',
          isPrimary: true,
          sortOrder: 1,
        },
      ],
      variants: [
        { id: '6', name: 'Size', type: 'size', value: '11oz', priceModifier: 0, sku: 'MUG-11OZ', stock: 50, isAvailable: true },
        { id: '7', name: 'Size', type: 'size', value: '15oz', priceModifier: 3, sku: 'MUG-15OZ', stock: 50, isAvailable: true },
        { id: '8', name: 'Color', type: 'color', value: 'White', priceModifier: 0, sku: 'MUG-WHITE', stock: 50, isAvailable: true },
      ],
      printAreas: [
        { id: '2', name: 'Wrap Around', x: 0, y: 50, width: 300, height: 200, maxDpi: 300, allowedFileTypes: ['png', 'jpg'] },
      ],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tags: ['ceramic', 'coffee', 'mug'],
    },
    // Add more mock products...
  ];

  useEffect(() => {
    // Simulate API call
    setLoading(true);
    setTimeout(() => {
      let filteredProducts = [...mockProducts];

      // Apply filters
      if (filters.category) {
        filteredProducts = filteredProducts.filter(p => 
          p.category.slug === filters.category
        );
      }

      if (searchQuery) {
        filteredProducts = filteredProducts.filter(p =>
          p.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          p.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          p.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      }

      if (filters.minPrice) {
        filteredProducts = filteredProducts.filter(p => p.basePrice >= filters.minPrice!);
      }

      if (filters.maxPrice) {
        filteredProducts = filteredProducts.filter(p => p.basePrice <= filters.maxPrice!);
      }

      // Apply sorting
      switch (filters.sortBy) {
        case 'price_asc':
          filteredProducts.sort((a, b) => a.basePrice - b.basePrice);
          break;
        case 'price_desc':
          filteredProducts.sort((a, b) => b.basePrice - a.basePrice);
          break;
        case 'newest':
          filteredProducts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
          break;
        default:
          // Popular - keep original order
          break;
      }

      setProducts(filteredProducts);
      setLoading(false);
    }, 500);
  }, [filters, searchQuery]);

  const handleFilterChange = (newFilters: Partial<ProductFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);

    // Update URL params
    const params = new URLSearchParams();
    if (updatedFilters.category) params.set('category', updatedFilters.category);
    if (updatedFilters.minPrice) params.set('minPrice', updatedFilters.minPrice.toString());
    if (updatedFilters.maxPrice) params.set('maxPrice', updatedFilters.maxPrice.toString());
    if (updatedFilters.sortBy) params.set('sortBy', updatedFilters.sortBy);
    if (searchQuery) params.set('search', searchQuery);

    setSearchParams(params);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams);
    if (searchQuery) {
      params.set('search', searchQuery);
    } else {
      params.delete('search');
    }
    setSearchParams(params);
  };

  const categories = [
    { id: 'all', name: 'All Products', count: mockProducts.length },
    { id: 't-shirts', name: 'T-Shirts', count: 1 },
    { id: 'mugs', name: 'Mugs', count: 1 },
    { id: 'posters', name: 'Posters', count: 0 },
    { id: 'accessories', name: 'Accessories', count: 0 },
  ];

  return (
    <>
      <title>Products - PrintCraft</title>
      <meta name="description" content="Browse our collection of customizable products including t-shirts, mugs, posters, and accessories." />

      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Products</h1>
            
            {/* Search and Controls */}
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <form onSubmit={handleSearch} className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    type="text"
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </form>

              <div className="flex items-center gap-4">
                {/* View Mode Toggle */}
                <div className="flex border border-gray-300 rounded-lg">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 ${viewMode === 'grid' ? 'bg-primary-600 text-white' : 'text-gray-600'}`}
                  >
                    <Grid className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 ${viewMode === 'list' ? 'bg-primary-600 text-white' : 'text-gray-600'}`}
                  >
                    <List className="w-5 h-5" />
                  </button>
                </div>

                {/* Filters Toggle */}
                <Button
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden"
                >
                  <SlidersHorizontal className="w-5 h-5 mr-2" />
                  Filters
                </Button>

                {/* Sort Dropdown */}
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange({ sortBy: e.target.value as any })}
                  className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                >
                  <option value="popular">Most Popular</option>
                  <option value="newest">Newest</option>
                  <option value="price_asc">Price: Low to High</option>
                  <option value="price_desc">Price: High to Low</option>
                </select>
              </div>
            </div>
          </div>

          <div className="flex gap-8">
            {/* Sidebar Filters */}
            <div className={`w-64 flex-shrink-0 ${showFilters ? 'block' : 'hidden lg:block'}`}>
              <Card className="p-6 sticky top-4">
                <h3 className="font-semibold text-lg mb-4">Filters</h3>

                {/* Categories */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Categories</h4>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <label key={category.id} className="flex items-center">
                        <input
                          type="radio"
                          name="category"
                          value={category.id === 'all' ? '' : category.id}
                          checked={filters.category === (category.id === 'all' ? undefined : category.id)}
                          onChange={(e) => handleFilterChange({ 
                            category: e.target.value || undefined 
                          })}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">
                          {category.name} ({category.count})
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Price Range */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Price Range</h4>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={filters.minPrice || ''}
                      onChange={(e) => handleFilterChange({ 
                        minPrice: e.target.value ? Number(e.target.value) : undefined 
                      })}
                      className="text-sm"
                    />
                    <Input
                      type="number"
                      placeholder="Max"
                      value={filters.maxPrice || ''}
                      onChange={(e) => handleFilterChange({ 
                        maxPrice: e.target.value ? Number(e.target.value) : undefined 
                      })}
                      className="text-sm"
                    />
                  </div>
                </div>

                {/* Clear Filters */}
                <Button
                  variant="outline"
                  onClick={() => {
                    setFilters({});
                    setSearchQuery('');
                    setSearchParams({});
                  }}
                  className="w-full"
                >
                  Clear All Filters
                </Button>
              </Card>
            </div>

            {/* Products Grid */}
            <div className="flex-1">
              {loading ? (
                <div className="flex justify-center items-center h-64">
                  <LoadingSpinner size="lg" />
                </div>
              ) : products.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-500 text-lg">No products found matching your criteria.</p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setFilters({});
                      setSearchQuery('');
                      setSearchParams({});
                    }}
                    className="mt-4"
                  >
                    Clear Filters
                  </Button>
                </div>
              ) : (
                <div className={`grid gap-6 ${
                  viewMode === 'grid' 
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' 
                    : 'grid-cols-1'
                }`}>
                  {products.map((product) => (
                    <ProductCard
                      key={product.id}
                      product={product}
                      viewMode={viewMode}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

interface ProductCardProps {
  product: Product;
  viewMode: 'grid' | 'list';
}

const ProductCard: React.FC<ProductCardProps> = ({ product, viewMode }) => {
  const { addItem } = useCart();

  const handleAddToCart = () => {
    addItem({
      productId: product.id,
      product,
      variants: [product.variants[0]], // Default variant
      quantity: 1,
      unitPrice: product.basePrice,
    });
  };

  if (viewMode === 'list') {
    return (
      <Card className="p-6 flex gap-6">
        <img
          src={product.images[0]?.url}
          alt={product.name}
          className="w-32 h-32 object-cover rounded-lg"
        />
        <div className="flex-1">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            <Link to={`/products/${product.id}`} className="hover:text-primary-600">
              {product.name}
            </Link>
          </h3>
          <p className="text-gray-600 mb-4">{product.description}</p>
          <div className="flex items-center justify-between">
            <span className="text-2xl font-bold text-primary-600">
              ${product.basePrice}
            </span>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Heart className="w-4 h-4" />
              </Button>
              <Button size="sm" onClick={handleAddToCart}>
                <ShoppingCart className="w-4 h-4 mr-2" />
                Add to Cart
              </Button>
              <Button variant="outline" size="sm">
                <Link to={`/design?product=${product.id}`}>Customize</Link>
              </Button>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="group overflow-hidden">
      <div className="aspect-square relative overflow-hidden">
        <img
          src={product.images[0]?.url}
          alt={product.name}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button variant="outline" size="sm" className="bg-white">
            <Heart className="w-4 h-4" />
          </Button>
        </div>
      </div>
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-2">
          <Link to={`/products/${product.id}`} className="hover:text-primary-600">
            {product.name}
          </Link>
        </h3>
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.description}</p>
        <div className="flex items-center justify-between mb-3">
          <span className="text-lg font-bold text-primary-600">
            ${product.basePrice}
          </span>
          <span className="text-sm text-gray-500">
            {product.variants.length} variants
          </span>
        </div>
        <div className="flex gap-2">
          <Button size="sm" onClick={handleAddToCart} className="flex-1">
            <ShoppingCart className="w-4 h-4 mr-2" />
            Add to Cart
          </Button>
          <Button variant="outline" size="sm">
            <Link to={`/design?product=${product.id}`}>Customize</Link>
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ProductCatalogPage;
