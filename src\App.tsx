import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';


// Layout Components
import Layout from './components/layout/Layout';
import AdminLayout from './components/layout/AdminLayout';

// Public Pages
import HomePage from './pages/public/HomePage';
import ProductCatalogPage from './pages/public/ProductCatalogPage';
import ProductDetailPage from './pages/public/ProductDetailPage';
import DesignEditorPage from './pages/public/DesignEditorPage';
import CartPage from './pages/public/CartPage';
import CheckoutPage from './pages/public/CheckoutPage';

// Auth Pages
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';

// User Dashboard Pages
import UserDashboard from './pages/dashboard/UserDashboard';
import OrderHistoryPage from './pages/dashboard/OrderHistoryPage';
import WishlistPage from './pages/dashboard/WishlistPage';

// Creator Dashboard Pages
import CreatorDashboard from './pages/creator/CreatorDashboard';
import DesignLibraryPage from './pages/creator/DesignLibraryPage';
import SalesAnalyticsPage from './pages/creator/SalesAnalyticsPage';
import PayoutPage from './pages/creator/PayoutPage';

// Admin Pages
import AdminDashboard from './pages/admin/AdminDashboard';
import UserManagementPage from './pages/admin/UserManagementPage';
import ProductManagementPage from './pages/admin/ProductManagementPage';
import OrderManagementPage from './pages/admin/OrderManagementPage';
import FinancialManagementPage from './pages/admin/FinancialManagementPage';

// Context Providers
import { AuthProvider } from './contexts/AuthContext';
import { CartProvider } from './contexts/CartContext';
import { ThemeProvider } from './contexts/ThemeContext';

// Protected Route Component
import ProtectedRoute from './components/auth/ProtectedRoute';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <AuthProvider>
            <CartProvider>
              <Router>
                <div className="min-h-screen bg-gray-50">
                  <Routes>
                    {/* Public Routes */}
                    <Route path="/" element={<Layout />}>
                      <Route index element={<HomePage />} />
                      <Route path="products" element={<ProductCatalogPage />} />
                      <Route path="products/:id" element={<ProductDetailPage />} />
                      <Route path="design/:productId?" element={<DesignEditorPage />} />
                      <Route path="cart" element={<CartPage />} />
                      <Route path="checkout" element={<CheckoutPage />} />
                    </Route>

                    {/* Auth Routes */}
                    <Route path="/auth/login" element={<LoginPage />} />
                    <Route path="/auth/register" element={<RegisterPage />} />

                    {/* User Dashboard Routes */}
                    <Route path="/dashboard" element={
                      <ProtectedRoute>
                        <Layout />
                      </ProtectedRoute>
                    }>
                      <Route index element={<UserDashboard />} />
                      <Route path="orders" element={<OrderHistoryPage />} />
                      <Route path="wishlist" element={<WishlistPage />} />
                    </Route>

                    {/* Creator Dashboard Routes */}
                    <Route path="/creator" element={
                      <ProtectedRoute requiredRole="creator">
                        <Layout />
                      </ProtectedRoute>
                    }>
                      <Route index element={<CreatorDashboard />} />
                      <Route path="designs" element={<DesignLibraryPage />} />
                      <Route path="analytics" element={<SalesAnalyticsPage />} />
                      <Route path="payouts" element={<PayoutPage />} />
                    </Route>

                    {/* Admin Routes */}
                    <Route path="/admin" element={
                      <ProtectedRoute requiredRole="admin">
                        <AdminLayout />
                      </ProtectedRoute>
                    }>
                      <Route index element={<AdminDashboard />} />
                      <Route path="users" element={<UserManagementPage />} />
                      <Route path="products" element={<ProductManagementPage />} />
                      <Route path="orders" element={<OrderManagementPage />} />
                      <Route path="financial" element={<FinancialManagementPage />} />
                    </Route>
                  </Routes>
                  
                  <Toaster 
                    position="top-right"
                    toastOptions={{
                      duration: 4000,
                      style: {
                        background: '#363636',
                        color: '#fff',
                      },
                    }}
                  />
                </div>
              </Router>
            </CartProvider>
          </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
  );
};

export default App;
