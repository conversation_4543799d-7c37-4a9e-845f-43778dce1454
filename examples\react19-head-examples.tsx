// React 19 Head Management Examples

// 1. Basic page title and meta tags
function HomePage() {
  return (
    <>
      <title>Home - Print on Demand Platform</title>
      <meta name="description" content="Create custom designs and print on demand products" />
      <meta name="keywords" content="print on demand, custom design, t-shirts" />
      
      <div>
        <h1>Welcome to our platform</h1>
        {/* Your page content */}
      </div>
    </>
  );
}

// 2. Dynamic title based on props/state
function ProductPage({ product }) {
  return (
    <>
      <title>{product.name} - Print on Demand Platform</title>
      <meta name="description" content={product.description} />
      <meta property="og:title" content={product.name} />
      <meta property="og:description" content={product.description} />
      <meta property="og:image" content={product.imageUrl} />
      
      <div>
        <h1>{product.name}</h1>
        {/* Product details */}
      </div>
    </>
  );
}

// 3. Conditional meta tags
function BlogPost({ post, isPublished }) {
  return (
    <>
      <title>{post.title} - Blog</title>
      <meta name="description" content={post.excerpt} />
      
      {isPublished && (
        <>
          <meta property="og:type" content="article" />
          <meta property="article:published_time" content={post.publishedAt} />
        </>
      )}
      
      {!isPublished && <meta name="robots" content="noindex, nofollow" />}
      
      <div>
        <h1>{post.title}</h1>
        {/* Blog post content */}
      </div>
    </>
  );
}

// 4. Using with React Router for different pages
function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/product/:id" element={<ProductPage />} />
        <Route path="/blog/:slug" element={<BlogPost />} />
      </Routes>
    </Router>
  );
}

// 5. Custom hook for common meta tags
function usePageMeta(title: string, description: string, image?: string) {
  return (
    <>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      {image && <meta property="og:image" content={image} />}
      <meta name="twitter:card" content="summary_large_image" />
    </>
  );
}

// Usage of the custom hook
function CustomPage() {
  const meta = usePageMeta(
    "Custom Page Title",
    "This is a custom page description",
    "/images/custom-page.jpg"
  );
  
  return (
    <>
      {meta}
      <div>
        <h1>Custom Page</h1>
        {/* Page content */}
      </div>
    </>
  );
}
