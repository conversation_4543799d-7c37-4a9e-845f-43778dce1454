{"name": "print-on-demand-platform", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@google/genai": "^1.20.0", "@hookform/resolvers": "^3.9.1", "@stripe/react-stripe-js": "^4.0.2", "@stripe/stripe-js": "^4.10.0", "axios": "^1.7.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "fabric": "^6.5.1", "framer-motion": "^11.15.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.14.0", "react-router-dom": "^6.28.0", "recharts": "^2.13.3", "stripe": "^17.5.0", "zod": "^3.23.8", "zustand": "^5.0.2"}, "devDependencies": {"@types/fabric": "^5.3.9", "@types/node": "^22.14.0", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^5.0.0", "@vitest/ui": "^2.1.8", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "~5.8.2", "vite": "^6.2.0", "vitest": "^2.1.8"}}