import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>R<PERSON>, Palette, Shirt, Coffee, Star, Users, Zap, Shield } from 'lucide-react';

import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';

const HomePage: React.FC = () => {
  const featuredCategories = [
    {
      id: 1,
      name: 'T-Shirts',
      image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',
      icon: <Shirt className="w-6 h-6" />,
      count: '500+ designs',
    },
    {
      id: 2,
      name: 'Mugs',
      image: 'https://images.unsplash.com/photo-1514228742587-6b1558fcf93a?w=400&h=400&fit=crop',
      icon: <Coffee className="w-6 h-6" />,
      count: '300+ designs',
    },
    {
      id: 3,
      name: 'Posters',
      image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=400&fit=crop',
      icon: <Palette className="w-6 h-6" />,
      count: '1000+ designs',
    },
    {
      id: 4,
      name: 'Accessories',
      image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',
      icon: <Star className="w-6 h-6" />,
      count: '200+ designs',
    },
  ];

  const testimonials = [
    {
      id: 1,
      name: 'Sarah Johnson',
      role: 'Small Business Owner',
      content: 'PrintCraft helped me create amazing branded merchandise for my business. The quality is outstanding!',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      rating: 5,
    },
    {
      id: 2,
      name: 'Mike Chen',
      role: 'Graphic Designer',
      content: 'As a creator, I love how easy it is to upload my designs and start earning. The platform is intuitive and profitable.',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      rating: 5,
    },
    {
      id: 3,
      name: 'Emily Davis',
      role: 'Customer',
      content: 'The design editor is so user-friendly! I created a custom gift for my friend and she absolutely loved it.',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      rating: 5,
    },
  ];

  const features = [
    {
      icon: <Palette className="w-8 h-8 text-primary-600" />,
      title: 'AI-Powered Design',
      description: 'Create stunning designs with our AI assistant or upload your own artwork.',
    },
    {
      icon: <Zap className="w-8 h-8 text-primary-600" />,
      title: 'Fast Production',
      description: 'Your orders are printed and shipped within 2-3 business days.',
    },
    {
      icon: <Shield className="w-8 h-8 text-primary-600" />,
      title: 'Quality Guarantee',
      description: '100% satisfaction guarantee with premium materials and printing.',
    },
    {
      icon: <Users className="w-8 h-8 text-primary-600" />,
      title: 'Creator Community',
      description: 'Join thousands of creators earning money from their designs.',
    },
  ];

  const howItWorks = [
    {
      step: 1,
      title: 'Choose a Product',
      description: 'Select from our wide range of customizable products.',
      icon: <Shirt className="w-12 h-12 text-primary-600" />,
    },
    {
      step: 2,
      title: 'Design & Customize',
      description: 'Use our design editor or upload your own artwork.',
      icon: <Palette className="w-12 h-12 text-primary-600" />,
    },
    {
      step: 3,
      title: 'Place Your Order',
      description: 'Review your design and complete your purchase.',
      icon: <Star className="w-12 h-12 text-primary-600" />,
    },
    {
      step: 4,
      title: 'Receive Your Product',
      description: 'We print and ship your custom product directly to you.',
      icon: <Users className="w-12 h-12 text-primary-600" />,
    },
  ];

  return (
    <>
      <title>PrintCraft - Custom Print-on-Demand Products</title>
      <meta name="description" content="Create and order custom printed products with our AI-powered design tools. T-shirts, mugs, posters, and more!" />
      <meta name="keywords" content="print on demand, custom products, t-shirts, mugs, design, AI" />

      <div className="min-h-screen">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white">
          <div className="absolute inset-0 bg-black opacity-20"></div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Create Amazing
                <span className="block text-yellow-300">Custom Products</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
                Design and order custom printed products with our AI-powered tools. 
                From t-shirts to mugs, bring your ideas to life!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-yellow-500 text-black hover:bg-yellow-400">
                  <Link to="/design" className="flex items-center">
                    Start Designing
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
                  <Link to="/products">Browse Products</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Categories */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Popular Categories
              </h2>
              <p className="text-lg text-gray-600">
                Discover our most popular product categories
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredCategories.map((category) => (
                <Link key={category.id} to={`/products?category=${category.name.toLowerCase()}`}>
                  <Card className="group hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                    <div className="aspect-square relative">
                      <img
                        src={category.image}
                        alt={category.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-30 transition-opacity duration-300"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center text-white">
                          {category.icon}
                          <h3 className="text-xl font-semibold mt-2">{category.name}</h3>
                          <p className="text-sm opacity-90">{category.count}</p>
                        </div>
                      </div>
                    </div>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </section>

        {/* How It Works */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                How It Works
              </h2>
              <p className="text-lg text-gray-600">
                Create custom products in just 4 simple steps
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {howItWorks.map((step) => (
                <div key={step.step} className="text-center">
                  <div className="relative mb-6">
                    <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto">
                      {step.icon}
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {step.step}
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {step.title}
                  </h3>
                  <p className="text-gray-600">
                    {step.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose PrintCraft?
              </h2>
              <p className="text-lg text-gray-600">
                We provide the best tools and service for your custom printing needs
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="text-center">
                  <div className="mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                What Our Customers Say
              </h2>
              <p className="text-lg text-gray-600">
                Join thousands of satisfied customers and creators
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {testimonials.map((testimonial) => (
                <Card key={testimonial.id} className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4">"{testimonial.content}"</p>
                  <div className="flex items-center">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      className="w-10 h-10 rounded-full mr-3"
                    />
                    <div>
                      <p className="font-semibold text-gray-900">{testimonial.name}</p>
                      <p className="text-sm text-gray-500">{testimonial.role}</p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary-600 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-4">
              Ready to Start Creating?
            </h2>
            <p className="text-xl mb-8 text-blue-100">
              Join our community of creators and customers today!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-yellow-500 text-black hover:bg-yellow-400">
                <Link to="/auth/register">Sign Up Free</Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
                <Link to="/design">Try Design Tool</Link>
              </Button>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default HomePage;
