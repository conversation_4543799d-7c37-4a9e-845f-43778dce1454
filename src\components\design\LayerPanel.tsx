import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, Trash2, <PERSON><PERSON>, ChevronUp, ChevronDown, Settings } from 'lucide-react';
import { DesignElement } from '../../types';
import Button from '../ui/Button';
import Card from '../ui/Card';
import Input from '../ui/Input';

interface LayerPanelProps {
  elements: DesignElement[];
  selectedElement: string | null;
  onSelectElement: (elementId: string | null) => void;
  onUpdateElement: (elementId: string, updates: Partial<DesignElement>) => void;
  onDeleteElement: (elementId: string) => void;
}

const LayerPanel: React.FC<LayerPanelProps> = ({
  elements,
  selectedElement,
  onSelectElement,
  onUpdateElement,
  onDeleteElement,
}) => {
  const [showProperties, setShowProperties] = useState(true);

  const sortedElements = [...elements].sort((a, b) => b.zIndex - a.zIndex);

  const handleMoveLayer = (elementId: string, direction: 'up' | 'down') => {
    const element = elements.find(el => el.id === elementId);
    if (!element) return;

    const newZIndex = direction === 'up' ? element.zIndex + 1 : element.zIndex - 1;
    onUpdateElement(elementId, { zIndex: Math.max(0, newZIndex) });
  };

  const handleDuplicateElement = (elementId: string) => {
    const element = elements.find(el => el.id === elementId);
    if (!element) return;

    const newElement: DesignElement = {
      ...element,
      id: `element-${Date.now()}`,
      position: {
        x: element.position.x + 10,
        y: element.position.y + 10,
      },
      zIndex: elements.length,
    };

    // This would need to be handled by the parent component
    // For now, we'll just show a placeholder
    console.log('Duplicate element:', newElement);
  };

  const selectedElementData = elements.find(el => el.id === selectedElement);

  return (
    <div className="p-4 space-y-4">
      {/* Layers List */}
      <div>
        <h3 className="font-medium text-gray-900 mb-3">Layers ({elements.length})</h3>
        
        {elements.length === 0 ? (
          <Card className="p-4 text-center">
            <p className="text-sm text-gray-500">No layers yet. Add some elements to get started!</p>
          </Card>
        ) : (
          <div className="space-y-2">
            {sortedElements.map((element) => (
              <LayerItem
                key={element.id}
                element={element}
                isSelected={selectedElement === element.id}
                onSelect={() => onSelectElement(element.id)}
                onToggleVisibility={(visible) => 
                  onUpdateElement(element.id, { opacity: visible ? 1 : 0 })
                }
                onMoveUp={() => handleMoveLayer(element.id, 'up')}
                onMoveDown={() => handleMoveLayer(element.id, 'down')}
                onDuplicate={() => handleDuplicateElement(element.id)}
                onDelete={() => onDeleteElement(element.id)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Properties Panel */}
      {selectedElementData && (
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">Properties</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowProperties(!showProperties)}
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>

          {showProperties && (
            <Card className="p-4 space-y-4">
              {/* Position */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Position</label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="number"
                    placeholder="X"
                    value={Math.round(selectedElementData.position.x)}
                    onChange={(e) => onUpdateElement(selectedElement!, {
                      position: { ...selectedElementData.position, x: Number(e.target.value) }
                    })}
                  />
                  <Input
                    type="number"
                    placeholder="Y"
                    value={Math.round(selectedElementData.position.y)}
                    onChange={(e) => onUpdateElement(selectedElement!, {
                      position: { ...selectedElementData.position, y: Number(e.target.value) }
                    })}
                  />
                </div>
              </div>

              {/* Size */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Size</label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="number"
                    placeholder="Width"
                    value={Math.round(selectedElementData.size.width)}
                    onChange={(e) => onUpdateElement(selectedElement!, {
                      size: { ...selectedElementData.size, width: Number(e.target.value) }
                    })}
                  />
                  <Input
                    type="number"
                    placeholder="Height"
                    value={Math.round(selectedElementData.size.height)}
                    onChange={(e) => onUpdateElement(selectedElement!, {
                      size: { ...selectedElementData.size, height: Number(e.target.value) }
                    })}
                  />
                </div>
              </div>

              {/* Rotation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rotation ({selectedElementData.rotation}°)
                </label>
                <input
                  type="range"
                  min="-180"
                  max="180"
                  value={selectedElementData.rotation}
                  onChange={(e) => onUpdateElement(selectedElement!, {
                    rotation: Number(e.target.value)
                  })}
                  className="w-full"
                />
              </div>

              {/* Opacity */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Opacity ({Math.round(selectedElementData.opacity * 100)}%)
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={selectedElementData.opacity}
                  onChange={(e) => onUpdateElement(selectedElement!, {
                    opacity: Number(e.target.value)
                  })}
                  className="w-full"
                />
              </div>

              {/* Text-specific properties */}
              {selectedElementData.type === 'text' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Text Content</label>
                    <Input
                      type="text"
                      value={selectedElementData.content}
                      onChange={(e) => onUpdateElement(selectedElement!, {
                        content: e.target.value
                      })}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                    <Input
                      type="number"
                      value={selectedElementData.style?.fontSize || 16}
                      onChange={(e) => onUpdateElement(selectedElement!, {
                        style: { ...selectedElementData.style, fontSize: Number(e.target.value) }
                      })}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Color</label>
                    <input
                      type="color"
                      value={selectedElementData.style?.color || '#000000'}
                      onChange={(e) => onUpdateElement(selectedElement!, {
                        style: { ...selectedElementData.style, color: e.target.value }
                      })}
                      className="w-full h-10 border border-gray-300 rounded"
                    />
                  </div>
                </>
              )}

              {/* Shape-specific properties */}
              {selectedElementData.type === 'shape' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Background Color</label>
                  <input
                    type="color"
                    value={selectedElementData.style?.backgroundColor || '#3b82f6'}
                    onChange={(e) => onUpdateElement(selectedElement!, {
                      style: { ...selectedElementData.style, backgroundColor: e.target.value }
                    })}
                    className="w-full h-10 border border-gray-300 rounded"
                  />
                </div>
              )}
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

interface LayerItemProps {
  element: DesignElement;
  isSelected: boolean;
  onSelect: () => void;
  onToggleVisibility: (visible: boolean) => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
}

const LayerItem: React.FC<LayerItemProps> = ({
  element,
  isSelected,
  onSelect,
  onToggleVisibility,
  onMoveUp,
  onMoveDown,
  onDuplicate,
  onDelete,
}) => {
  const getElementName = () => {
    switch (element.type) {
      case 'text':
        return `Text: ${element.content.substring(0, 20)}${element.content.length > 20 ? '...' : ''}`;
      case 'image':
        return 'Image';
      case 'shape':
        return `Shape: ${element.content}`;
      default:
        return 'Element';
    }
  };

  const isVisible = element.opacity > 0;

  return (
    <div
      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
        isSelected ? 'border-primary-600 bg-primary-50' : 'border-gray-200 hover:border-gray-300'
      }`}
      onClick={onSelect}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleVisibility(!isVisible);
            }}
            className="text-gray-400 hover:text-gray-600"
          >
            {isVisible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          </button>
          <span className="text-sm font-medium text-gray-900 truncate">
            {getElementName()}
          </span>
        </div>

        <div className="flex items-center space-x-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onMoveUp();
            }}
            className="text-gray-400 hover:text-gray-600"
          >
            <ChevronUp className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onMoveDown();
            }}
            className="text-gray-400 hover:text-gray-600"
          >
            <ChevronDown className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDuplicate();
            }}
            className="text-gray-400 hover:text-gray-600"
          >
            <Copy className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="text-gray-400 hover:text-red-600"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>

      <div className="mt-2 text-xs text-gray-500">
        Layer {element.zIndex} • {element.type} • {Math.round(element.opacity * 100)}% opacity
      </div>
    </div>
  );
};

export default LayerPanel;
