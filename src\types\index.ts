// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  avatar?: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
  profile?: UserProfile;
}

export type UserRole = 'customer' | 'creator' | 'admin';

export interface UserProfile {
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  address?: Address;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  newsletter: boolean;
  smsNotifications: boolean;
  emailNotifications: boolean;
  currency: string;
  language: string;
}

export interface Address {
  id?: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault?: boolean;
}

// Product Types
export interface Product {
  id: string;
  name: string;
  description: string;
  category: ProductCategory;
  basePrice: number;
  images: ProductImage[];
  variants: ProductVariant[];
  printAreas: PrintArea[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  seo?: SEOData;
}

export interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  image?: string;
}

export interface ProductImage {
  id: string;
  url: string;
  alt: string;
  isPrimary: boolean;
  sortOrder: number;
}

export interface ProductVariant {
  id: string;
  name: string;
  type: 'size' | 'color' | 'material';
  value: string;
  priceModifier: number;
  sku: string;
  stock: number;
  isAvailable: boolean;
}

export interface PrintArea {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  maxDpi: number;
  allowedFileTypes: string[];
}

// Design Types
export interface Design {
  id: string;
  name: string;
  description?: string;
  creatorId: string;
  creator?: User;
  imageUrl: string;
  thumbnailUrl: string;
  tags: string[];
  category: string;
  isPublic: boolean;
  price?: number;
  downloads: number;
  likes: number;
  createdAt: string;
  updatedAt: string;
  elements: DesignElement[];
}

export interface DesignElement {
  id: string;
  type: 'image' | 'text' | 'shape';
  content: string;
  position: Position;
  size: Size;
  rotation: number;
  opacity: number;
  zIndex: number;
  style?: ElementStyle;
}

export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface ElementStyle {
  color?: string;
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: string;
  textAlign?: 'left' | 'center' | 'right';
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
}

// Order Types
export interface Order {
  id: string;
  userId: string;
  user?: User;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  status: OrderStatus;
  shippingAddress: Address;
  billingAddress: Address;
  paymentMethod: PaymentMethod;
  trackingNumber?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export type OrderStatus = 
  | 'pending' 
  | 'confirmed' 
  | 'processing' 
  | 'printed' 
  | 'shipped' 
  | 'delivered' 
  | 'cancelled' 
  | 'refunded';

export interface OrderItem {
  id: string;
  productId: string;
  product?: Product;
  designId?: string;
  design?: Design;
  customDesign?: CustomDesign;
  variants: ProductVariant[];
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface CustomDesign {
  elements: DesignElement[];
  printArea: PrintArea;
  previewUrl: string;
}

// Cart Types
export interface CartItem {
  id: string;
  productId: string;
  product?: Product;
  designId?: string;
  design?: Design;
  customDesign?: CustomDesign;
  variants: ProductVariant[];
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

// Payment Types
export interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'stripe' | 'bank_transfer';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

// Analytics Types
export interface SalesAnalytics {
  totalRevenue: number;
  totalOrders: number;
  totalDesigns: number;
  conversionRate: number;
  topSellingDesigns: Design[];
  revenueByMonth: MonthlyRevenue[];
  ordersByStatus: OrderStatusCount[];
}

export interface MonthlyRevenue {
  month: string;
  revenue: number;
  orders: number;
}

export interface OrderStatusCount {
  status: OrderStatus;
  count: number;
}

// SEO Types
export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  ogImage?: string;
  canonicalUrl?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Filter and Search Types
export interface ProductFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  colors?: string[];
  sizes?: string[];
  materials?: string[];
  tags?: string[];
  sortBy?: 'price_asc' | 'price_desc' | 'newest' | 'popular' | 'rating';
}

export interface SearchParams {
  query?: string;
  filters?: ProductFilters;
  page?: number;
  limit?: number;
}
