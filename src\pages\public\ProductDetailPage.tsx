import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Heart, ShoppingCart, Star, Truck, Shield, RotateCcw, Palette } from 'lucide-react';

import { Product, ProductVariant } from '../../types';
import { useCart } from '../../contexts/CartContext';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import LoadingSpinner from '../../components/ui/LoadingSpinner';

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedVariants, setSelectedVariants] = useState<{ [key: string]: ProductVariant }>({});
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const { addItem } = useCart();

  // Mock product data
  const mockProduct: Product = {
    id: '1',
    name: 'Classic Cotton T-Shirt',
    description: 'Our premium 100% cotton t-shirt is the perfect canvas for your custom designs. Made from soft, breathable fabric that feels great against your skin and maintains its shape wash after wash. This classic fit tee is ideal for everyday wear, special events, or promotional purposes.',
    category: { id: '1', name: 'T-Shirts', slug: 't-shirts' },
    basePrice: 19.99,
    images: [
      {
        id: '1',
        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=600&h=600&fit=crop',
        alt: 'Classic Cotton T-Shirt - Front View',
        isPrimary: true,
        sortOrder: 1,
      },
      {
        id: '2',
        url: 'https://images.unsplash.com/photo-1583743814966-8936f37f4678?w=600&h=600&fit=crop',
        alt: 'Classic Cotton T-Shirt - Back View',
        isPrimary: false,
        sortOrder: 2,
      },
      {
        id: '3',
        url: 'https://images.unsplash.com/photo-1576566588028-4147f3842f27?w=600&h=600&fit=crop',
        alt: 'Classic Cotton T-Shirt - Side View',
        isPrimary: false,
        sortOrder: 3,
      },
    ],
    variants: [
      { id: '1', name: 'Size', type: 'size', value: 'XS', priceModifier: 0, sku: 'TSHIRT-XS', stock: 50, isAvailable: true },
      { id: '2', name: 'Size', type: 'size', value: 'S', priceModifier: 0, sku: 'TSHIRT-S', stock: 100, isAvailable: true },
      { id: '3', name: 'Size', type: 'size', value: 'M', priceModifier: 0, sku: 'TSHIRT-M', stock: 100, isAvailable: true },
      { id: '4', name: 'Size', type: 'size', value: 'L', priceModifier: 2, sku: 'TSHIRT-L', stock: 100, isAvailable: true },
      { id: '5', name: 'Size', type: 'size', value: 'XL', priceModifier: 2, sku: 'TSHIRT-XL', stock: 75, isAvailable: true },
      { id: '6', name: 'Size', type: 'size', value: 'XXL', priceModifier: 4, sku: 'TSHIRT-XXL', stock: 50, isAvailable: true },
      { id: '7', name: 'Color', type: 'color', value: 'White', priceModifier: 0, sku: 'TSHIRT-WHITE', stock: 200, isAvailable: true },
      { id: '8', name: 'Color', type: 'color', value: 'Black', priceModifier: 0, sku: 'TSHIRT-BLACK', stock: 150, isAvailable: true },
      { id: '9', name: 'Color', type: 'color', value: 'Navy', priceModifier: 1, sku: 'TSHIRT-NAVY', stock: 100, isAvailable: true },
      { id: '10', name: 'Color', type: 'color', value: 'Gray', priceModifier: 0, sku: 'TSHIRT-GRAY', stock: 120, isAvailable: true },
    ],
    printAreas: [
      { id: '1', name: 'Front', x: 150, y: 100, width: 200, height: 300, maxDpi: 300, allowedFileTypes: ['png', 'jpg', 'svg'] },
      { id: '2', name: 'Back', x: 150, y: 100, width: 200, height: 300, maxDpi: 300, allowedFileTypes: ['png', 'jpg', 'svg'] },
    ],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    tags: ['cotton', 'classic', 'comfortable', 'unisex'],
  };

  useEffect(() => {
    // Simulate API call
    setLoading(true);
    setTimeout(() => {
      setProduct(mockProduct);
      
      // Set default variants
      const defaultVariants: { [key: string]: ProductVariant } = {};
      const variantTypes = [...new Set(mockProduct.variants.map(v => v.type))];
      
      variantTypes.forEach(type => {
        const firstVariantOfType = mockProduct.variants.find(v => v.type === type);
        if (firstVariantOfType) {
          defaultVariants[type] = firstVariantOfType;
        }
      });
      
      setSelectedVariants(defaultVariants);
      setLoading(false);
    }, 500);
  }, [id]);

  const handleVariantChange = (type: string, variant: ProductVariant) => {
    setSelectedVariants(prev => ({
      ...prev,
      [type]: variant,
    }));
  };

  const calculatePrice = () => {
    if (!product) return 0;
    
    const basePrice = product.basePrice;
    const variantModifiers = Object.values(selectedVariants).reduce(
      (sum, variant) => sum + variant.priceModifier,
      0
    );
    
    return basePrice + variantModifiers;
  };

  const handleAddToCart = () => {
    if (!product) return;
    
    addItem({
      productId: product.id,
      product,
      variants: Object.values(selectedVariants),
      quantity,
      unitPrice: calculatePrice(),
    });
  };

  const groupedVariants = product?.variants.reduce((acc, variant) => {
    if (!acc[variant.type]) {
      acc[variant.type] = [];
    }
    acc[variant.type].push(variant);
    return acc;
  }, {} as { [key: string]: ProductVariant[] }) || {};

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <Link to="/products">
            <Button>Browse Products</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <title>{product.name} - PrintCraft</title>
      <meta name="description" content={product.description} />

      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <ol className="flex items-center space-x-2 text-sm text-gray-500">
              <li><Link to="/" className="hover:text-primary-600">Home</Link></li>
              <li>/</li>
              <li><Link to="/products" className="hover:text-primary-600">Products</Link></li>
              <li>/</li>
              <li><Link to={`/products?category=${product.category.slug}`} className="hover:text-primary-600">{product.category.name}</Link></li>
              <li>/</li>
              <li className="text-gray-900">{product.name}</li>
            </ol>
          </nav>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Product Images */}
            <div>
              <div className="aspect-square mb-4">
                <img
                  src={product.images[selectedImageIndex]?.url}
                  alt={product.images[selectedImageIndex]?.alt}
                  className="w-full h-full object-cover rounded-lg"
                />
              </div>
              
              {/* Image Thumbnails */}
              <div className="flex gap-2">
                {product.images.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`w-20 h-20 rounded-lg overflow-hidden border-2 ${
                      selectedImageIndex === index ? 'border-primary-600' : 'border-gray-200'
                    }`}
                  >
                    <img
                      src={image.url}
                      alt={image.alt}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>

            {/* Product Info */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{product.name}</h1>
              
              {/* Rating */}
              <div className="flex items-center mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <span className="ml-2 text-sm text-gray-600">(4.8) • 127 reviews</span>
              </div>

              {/* Price */}
              <div className="mb-6">
                <span className="text-3xl font-bold text-primary-600">
                  ${calculatePrice().toFixed(2)}
                </span>
                {calculatePrice() !== product.basePrice && (
                  <span className="ml-2 text-lg text-gray-500 line-through">
                    ${product.basePrice.toFixed(2)}
                  </span>
                )}
              </div>

              {/* Description */}
              <p className="text-gray-600 mb-6">{product.description}</p>

              {/* Variants */}
              <div className="space-y-6 mb-6">
                {Object.entries(groupedVariants).map(([type, variants]) => (
                  <div key={type}>
                    <h3 className="text-lg font-medium text-gray-900 mb-3 capitalize">
                      {type}: {selectedVariants[type]?.value}
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {variants.map((variant) => (
                        <button
                          key={variant.id}
                          onClick={() => handleVariantChange(type, variant)}
                          disabled={!variant.isAvailable}
                          className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
                            selectedVariants[type]?.id === variant.id
                              ? 'border-primary-600 bg-primary-600 text-white'
                              : variant.isAvailable
                              ? 'border-gray-300 text-gray-700 hover:border-primary-600'
                              : 'border-gray-200 text-gray-400 cursor-not-allowed'
                          }`}
                        >
                          {variant.value}
                          {variant.priceModifier > 0 && ` (+$${variant.priceModifier})`}
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {/* Quantity */}
              <div className="mb-6">
                <label className="block text-lg font-medium text-gray-900 mb-3">
                  Quantity
                </label>
                <div className="flex items-center">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="px-3 py-2 border border-gray-300 rounded-l-lg hover:bg-gray-50"
                  >
                    -
                  </button>
                  <span className="px-4 py-2 border-t border-b border-gray-300 bg-white">
                    {quantity}
                  </span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="px-3 py-2 border border-gray-300 rounded-r-lg hover:bg-gray-50"
                  >
                    +
                  </button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4 mb-8">
                <Button onClick={handleAddToCart} className="flex-1">
                  <ShoppingCart className="w-5 h-5 mr-2" />
                  Add to Cart
                </Button>
                <Button variant="outline">
                  <Heart className="w-5 h-5" />
                </Button>
              </div>

              {/* Customize Button */}
              <Link to={`/design?product=${product.id}`}>
                <Button variant="outline" className="w-full mb-8">
                  <Palette className="w-5 h-5 mr-2" />
                  Customize This Product
                </Button>
              </Link>

              {/* Features */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Truck className="w-5 h-5 mr-2 text-primary-600" />
                  Free shipping over $50
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Shield className="w-5 h-5 mr-2 text-primary-600" />
                  Quality guarantee
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <RotateCcw className="w-5 h-5 mr-2 text-primary-600" />
                  30-day returns
                </div>
              </div>
            </div>
          </div>

          {/* Product Details Tabs */}
          <div className="mt-16">
            <Card className="p-6">
              <div className="border-b border-gray-200 mb-6">
                <nav className="-mb-px flex space-x-8">
                  <button className="border-b-2 border-primary-600 py-2 px-1 text-sm font-medium text-primary-600">
                    Product Details
                  </button>
                  <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                    Size Guide
                  </button>
                  <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                    Reviews (127)
                  </button>
                  <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                    Shipping & Returns
                  </button>
                </nav>
              </div>

              <div className="prose max-w-none">
                <h3>Product Specifications</h3>
                <ul>
                  <li>Material: 100% Cotton</li>
                  <li>Weight: 180 GSM</li>
                  <li>Fit: Classic/Regular</li>
                  <li>Neckline: Crew neck</li>
                  <li>Sleeve: Short sleeve</li>
                  <li>Care: Machine wash cold, tumble dry low</li>
                </ul>

                <h3>Print Areas</h3>
                <ul>
                  {product.printAreas.map((area) => (
                    <li key={area.id}>
                      {area.name}: {area.width}x{area.height}px (Max DPI: {area.maxDpi})
                    </li>
                  ))}
                </ul>

                <h3>Available File Types</h3>
                <p>
                  {product.printAreas[0]?.allowedFileTypes.join(', ').toUpperCase()}
                </p>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductDetailPage;
