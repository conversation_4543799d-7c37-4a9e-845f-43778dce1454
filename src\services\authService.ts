import { apiService } from './api';
import { User, UserRole } from '../types';

interface LoginResponse {
  user: User;
  token: string;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: UserRole;
}

class AuthService {
  async login(email: string, password: string): Promise<LoginResponse> {
    try {
      const response = await apiService.post<LoginResponse>('/auth/login', {
        email,
        password,
      });
      return response;
    } catch (error) {
      // For demo purposes, return mock data
      console.warn('Using mock auth service');
      return this.mockLogin(email, password);
    }
  }

  async register(userData: RegisterData): Promise<LoginResponse> {
    try {
      const response = await apiService.post<LoginResponse>('/auth/register', userData);
      return response;
    } catch (error) {
      // For demo purposes, return mock data
      console.warn('Using mock auth service');
      return this.mockRegister(userData);
    }
  }

  async getCurrentUser(): Promise<User> {
    try {
      const response = await apiService.get<User>('/auth/me');
      return response;
    } catch (error) {
      // For demo purposes, return mock data
      console.warn('Using mock auth service');
      return this.mockGetCurrentUser();
    }
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    try {
      const response = await apiService.patch<User>('/auth/profile', userData);
      return response;
    } catch (error) {
      // For demo purposes, return mock data
      console.warn('Using mock auth service');
      return this.mockUpdateProfile(userData);
    }
  }

  async forgotPassword(email: string): Promise<void> {
    try {
      await apiService.post('/auth/forgot-password', { email });
    } catch (error) {
      console.warn('Using mock auth service');
      // Mock implementation - just log for demo
      console.log('Password reset email sent to:', email);
    }
  }

  async resetPassword(token: string, password: string): Promise<void> {
    try {
      await apiService.post('/auth/reset-password', { token, password });
    } catch (error) {
      console.warn('Using mock auth service');
      // Mock implementation
      console.log('Password reset successful');
    }
  }

  // Mock implementations for demo purposes
  private mockLogin(email: string, password: string): Promise<LoginResponse> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (email === '<EMAIL>' && password === 'admin123') {
          resolve({
            user: {
              id: '1',
              email: '<EMAIL>',
              firstName: 'Admin',
              lastName: 'User',
              role: 'admin',
              isVerified: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            token: 'mock-admin-token',
          });
        } else if (email === '<EMAIL>' && password === 'creator123') {
          resolve({
            user: {
              id: '2',
              email: '<EMAIL>',
              firstName: 'Creator',
              lastName: 'User',
              role: 'creator',
              isVerified: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            token: 'mock-creator-token',
          });
        } else if (email === '<EMAIL>' && password === 'customer123') {
          resolve({
            user: {
              id: '3',
              email: '<EMAIL>',
              firstName: 'Customer',
              lastName: 'User',
              role: 'customer',
              isVerified: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            token: 'mock-customer-token',
          });
        } else {
          reject(new Error('Invalid email or password'));
        }
      }, 1000);
    });
  }

  private mockRegister(userData: RegisterData): Promise<LoginResponse> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          user: {
            id: Date.now().toString(),
            email: userData.email,
            firstName: userData.firstName,
            lastName: userData.lastName,
            role: userData.role || 'customer',
            isVerified: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          token: `mock-token-${Date.now()}`,
        });
      }, 1000);
    });
  }

  private mockGetCurrentUser(): Promise<User> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const token = localStorage.getItem('authToken');
        if (token?.includes('admin')) {
          resolve({
            id: '1',
            email: '<EMAIL>',
            firstName: 'Admin',
            lastName: 'User',
            role: 'admin',
            isVerified: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });
        } else if (token?.includes('creator')) {
          resolve({
            id: '2',
            email: '<EMAIL>',
            firstName: 'Creator',
            lastName: 'User',
            role: 'creator',
            isVerified: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });
        } else if (token?.includes('customer')) {
          resolve({
            id: '3',
            email: '<EMAIL>',
            firstName: 'Customer',
            lastName: 'User',
            role: 'customer',
            isVerified: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });
        } else {
          reject(new Error('Invalid token'));
        }
      }, 500);
    });
  }

  private mockUpdateProfile(userData: Partial<User>): Promise<User> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
        const updatedUser = { ...currentUser, ...userData };
        localStorage.setItem('currentUser', JSON.stringify(updatedUser));
        resolve(updatedUser);
      }, 500);
    });
  }
}

export const authService = new AuthService();
