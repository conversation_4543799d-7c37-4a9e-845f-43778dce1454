# PrintCraft - Print-on-Demand Web Application

A comprehensive print-on-demand platform built with React, TypeScript, and modern web technologies. This application supports three main user types: Customers, Creators, and Administrators.

## 🚀 Features

### Customer Features
- **Product Browsing**: Browse and search through a catalog of customizable products
- **Design Editor**: Create custom designs with text, images, shapes, and AI-generated content
- **Shopping Cart**: Add items to cart with customization options
- **User Dashboard**: Track orders, manage wishlist, and view order history
- **Secure Checkout**: Complete purchases with integrated payment processing

### Creator Features
- **Creator Dashboard**: Manage designs and track sales performance
- **Design Library**: Upload and organize design portfolios
- **Sales Analytics**: View detailed sales reports and earnings
- **Payout Management**: Track earnings and manage payment methods

### Admin Features
- **Admin Dashboard**: Comprehensive overview of platform metrics
- **User Management**: Manage customers, creators, and administrators
- **Product Management**: Add, edit, and manage product catalog
- **Order Management**: Track and fulfill customer orders
- **Financial Management**: Monitor revenue, payouts, and financial reports

### Advanced Features
- **AI Design Generation**: Generate custom designs using Gemini AI
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **SEO Optimization**: Built-in SEO features for better search visibility
- **Real-time Updates**: Live updates for order status and notifications
- **Print Quality Control**: Automated validation for print-ready designs

## 🛠️ Technology Stack

- **Frontend**: React 19.1.1 with TypeScript
- **Routing**: React Router DOM v7
- **State Management**: Zustand + React Context API
- **Styling**: Tailwind CSS
- **Forms**: React Hook Form with Zod validation
- **Animations**: Framer Motion
- **Canvas**: Fabric.js for design editor
- **AI Integration**: Google Gemini API for image generation
- **Payment Processing**: Stripe integration
- **SEO**: React 19 Native Head Support
- **Build Tool**: Vite
- **Package Manager**: npm

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── design/         # Design editor components
│   ├── layout/         # Layout components (Header, Footer, etc.)
│   └── ui/             # Basic UI components (Button, Input, etc.)
├── contexts/           # React Context providers
├── pages/              # Page components
│   ├── admin/          # Admin panel pages
│   ├── auth/           # Authentication pages
│   ├── creator/        # Creator dashboard pages
│   ├── dashboard/      # User dashboard pages
│   └── public/         # Public pages
├── services/           # API services and utilities
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd printcraft
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```env
   VITE_API_URL=http://localhost:3001
   VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_key
   GEMINI_API_KEY=your_gemini_api_key
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:5173`

## 🔐 Demo Accounts

The application includes demo accounts for testing:

### Customer Account
- **Email**: <EMAIL>
- **Password**: password123

### Creator Account
- **Email**: <EMAIL>
- **Password**: password123

### Admin Account
- **Email**: <EMAIL>
- **Password**: password123

## 🎨 Design Editor Features

The design editor includes:
- **Text Tools**: Add and customize text with various fonts and styles
- **Image Upload**: Upload custom images and graphics
- **Shape Tools**: Add geometric shapes and customize colors
- **AI Generation**: Generate custom designs using AI prompts
- **Layer Management**: Organize design elements with layer controls
- **Canvas Manipulation**: Drag, resize, rotate, and position elements
- **Print Area Visualization**: See exactly how designs will appear on products

## 📱 Responsive Design

The application is fully responsive and optimized for:
- **Desktop**: Full-featured experience with all tools and panels
- **Tablet**: Optimized layout with touch-friendly controls
- **Mobile**: Streamlined interface for on-the-go access

## 🔒 Security Features

- **Authentication**: Secure user authentication with role-based access
- **Data Validation**: Client and server-side validation using Zod
- **Secure Payments**: PCI-compliant payment processing with Stripe
- **HTTPS**: SSL encryption for all data transmission
- **Input Sanitization**: Protection against XSS and injection attacks

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

### Deploy to Vercel
```bash
npm install -g vercel
vercel --prod
```

## 🧪 Testing

Run the test suite:
```bash
npm run test
```

Run tests with coverage:
```bash
npm run test:coverage
```

## 📈 Performance Optimization

- **Code Splitting**: Automatic route-based code splitting
- **Lazy Loading**: Components and images loaded on demand
- **Caching**: Optimized caching strategies for assets
- **Bundle Optimization**: Minimized bundle sizes with tree shaking
- **Image Optimization**: Responsive images with proper formats

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation wiki

## 🗺️ Roadmap

- [ ] Mobile app development (React Native)
- [ ] Advanced analytics and reporting
- [ ] Multi-language support
- [ ] Advanced AI features
- [ ] Marketplace integration
- [ ] Bulk order management
- [ ] Advanced design templates
- [ ] Social media integration

---

Built with ❤️ using React and TypeScript
