import React, { useState } from 'react';
import { Product } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Search } from 'lucide-react';

interface ProductSelectorProps {
  onProductSelect: (product: Product) => void;
}

const ProductSelector: React.FC<ProductSelectorProps> = ({ onProductSelect }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Mock products data
  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'Classic Cotton T-Shirt',
      description: 'Premium 100% cotton t-shirt',
      category: { id: '1', name: 'T-Shirts', slug: 't-shirts' },
      basePrice: 19.99,
      images: [
        {
          id: '1',
          url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=300&fit=crop',
          alt: 'Classic Cotton T-Shirt',
          isPrimary: true,
          sortOrder: 1,
        },
      ],
      variants: [],
      printAreas: [
        { id: '1', name: 'Front', x: 150, y: 100, width: 200, height: 300, maxDpi: 300, allowedFileTypes: ['png', 'jpg', 'svg'] },
      ],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tags: ['cotton', 'classic'],
    },
    {
      id: '2',
      name: 'Ceramic Coffee Mug',
      description: 'High-quality ceramic mug',
      category: { id: '2', name: 'Mugs', slug: 'mugs' },
      basePrice: 14.99,
      images: [
        {
          id: '2',
          url: 'https://images.unsplash.com/photo-1514228742587-6b1558fcf93a?w=300&h=300&fit=crop',
          alt: 'Ceramic Coffee Mug',
          isPrimary: true,
          sortOrder: 1,
        },
      ],
      variants: [],
      printAreas: [
        { id: '2', name: 'Wrap Around', x: 0, y: 50, width: 300, height: 200, maxDpi: 300, allowedFileTypes: ['png', 'jpg'] },
      ],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tags: ['ceramic', 'coffee'],
    },
    {
      id: '3',
      name: 'Canvas Poster',
      description: 'High-quality canvas print',
      category: { id: '3', name: 'Posters', slug: 'posters' },
      basePrice: 24.99,
      images: [
        {
          id: '3',
          url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=300&h=300&fit=crop',
          alt: 'Canvas Poster',
          isPrimary: true,
          sortOrder: 1,
        },
      ],
      variants: [],
      printAreas: [
        { id: '3', name: 'Full Canvas', x: 0, y: 0, width: 400, height: 600, maxDpi: 300, allowedFileTypes: ['png', 'jpg'] },
      ],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tags: ['canvas', 'poster'],
    },
  ];

  const categories = [
    { id: 'all', name: 'All Products' },
    { id: 't-shirts', name: 'T-Shirts' },
    { id: 'mugs', name: 'Mugs' },
    { id: 'posters', name: 'Posters' },
  ];

  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.category.slug === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="p-4">
      <h3 className="font-medium text-gray-900 mb-4">Choose a Product</h3>
      
      {/* Search */}
      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          type="text"
          placeholder="Search products..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Categories */}
      <div className="mb-4">
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                selectedCategory === category.id
                  ? 'bg-primary-600 text-white border-primary-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:border-primary-600'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Products Grid */}
      <div className="space-y-3">
        {filteredProducts.map((product) => (
          <Card key={product.id} className="p-3 hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center space-x-3">
              <img
                src={product.images[0]?.url}
                alt={product.name}
                className="w-16 h-16 object-cover rounded-lg"
              />
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 truncate">{product.name}</h4>
                <p className="text-sm text-gray-600 truncate">{product.description}</p>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-sm font-medium text-primary-600">
                    ${product.basePrice}
                  </span>
                  <span className="text-xs text-gray-500">
                    {product.printAreas.length} print area{product.printAreas.length !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>
            </div>
            <Button
              onClick={() => onProductSelect(product)}
              className="w-full mt-3"
              size="sm"
            >
              Select Product
            </Button>
          </Card>
        ))}
      </div>

      {filteredProducts.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No products found matching your criteria.</p>
          <Button
            variant="outline"
            onClick={() => {
              setSearchQuery('');
              setSelectedCategory('all');
            }}
            className="mt-2"
            size="sm"
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
};

export default ProductSelector;
