import React, { useState } from 'react';
import { Upload, Type, Square, Circle, Image, Sparkles } from 'lucide-react';
import { Product, DesignElement } from '../../types';
import Button from '../ui/Button';
import Card from '../ui/Card';
import Input from '../ui/Input';
import { generateImage } from '../../services/geminiService';

interface DesignToolbarProps {
  onAddElement: (element: Omit<DesignElement, 'id' | 'zIndex'>) => void;
  selectedProduct: Product;
}

const DesignToolbar: React.FC<DesignToolbarProps> = ({ onAddElement, selectedProduct }) => {
  const [activeTab, setActiveTab] = useState<'upload' | 'text' | 'shapes' | 'ai'>('upload');
  const [textInput, setTextInput] = useState('');
  const [aiPrompt, setAiPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const tabs = [
    { id: 'upload', label: 'Upload', icon: Upload },
    { id: 'text', label: 'Text', icon: Type },
    { id: 'shapes', label: 'Shapes', icon: Square },
    { id: 'ai', label: 'AI Generate', icon: Sparkles },
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      if (result) {
        onAddElement({
          type: 'image',
          content: result,
          position: { x: 50, y: 50 },
          size: { width: 100, height: 100 },
          rotation: 0,
          opacity: 1,
        });
      }
    };
    reader.readAsDataURL(file);
  };

  const handleAddText = () => {
    if (!textInput.trim()) return;

    onAddElement({
      type: 'text',
      content: textInput,
      position: { x: 50, y: 50 },
      size: { width: 200, height: 50 },
      rotation: 0,
      opacity: 1,
      style: {
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        textAlign: 'center',
      },
    });

    setTextInput('');
  };

  const handleAddShape = (shapeType: 'rectangle' | 'circle') => {
    onAddElement({
      type: 'shape',
      content: shapeType,
      position: { x: 50, y: 50 },
      size: { width: 100, height: 100 },
      rotation: 0,
      opacity: 1,
      style: {
        backgroundColor: '#3b82f6',
        borderRadius: shapeType === 'circle' ? 50 : 0,
      },
    });
  };

  const handleGenerateAI = async () => {
    if (!aiPrompt.trim()) return;

    setIsGenerating(true);
    try {
      const imageUrl = await generateImage(aiPrompt);
      if (imageUrl) {
        onAddElement({
          type: 'image',
          content: imageUrl,
          position: { x: 50, y: 50 },
          size: { width: 150, height: 150 },
          rotation: 0,
          opacity: 1,
        });
        setAiPrompt('');
      }
    } catch (error) {
      console.error('Failed to generate AI image:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="p-4">
      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200 mb-4">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center py-2 px-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-primary-600 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <tab.icon className="w-4 h-4 mr-1" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="space-y-4">
        {activeTab === 'upload' && (
          <Card className="p-4">
            <h3 className="font-medium text-gray-900 mb-3">Upload Image</h3>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Image className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-3">
                Drag and drop an image, or click to browse
              </p>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
              />
              <label htmlFor="file-upload">
                <Button variant="outline" className="cursor-pointer">
                  Choose File
                </Button>
              </label>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Supported formats: PNG, JPG, SVG (Max: 10MB)
            </p>
          </Card>
        )}

        {activeTab === 'text' && (
          <Card className="p-4">
            <h3 className="font-medium text-gray-900 mb-3">Add Text</h3>
            <div className="space-y-3">
              <Input
                type="text"
                placeholder="Enter your text..."
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddText()}
              />
              <Button onClick={handleAddText} className="w-full" disabled={!textInput.trim()}>
                Add Text
              </Button>
            </div>
            
            {/* Font Options */}
            <div className="mt-4 space-y-2">
              <h4 className="text-sm font-medium text-gray-700">Quick Styles</h4>
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={() => {
                    setTextInput('BOLD TEXT');
                    setTimeout(handleAddText, 100);
                  }}
                  className="p-2 border border-gray-300 rounded text-sm font-bold hover:bg-gray-50"
                >
                  Bold
                </button>
                <button
                  onClick={() => {
                    setTextInput('Italic Text');
                    setTimeout(handleAddText, 100);
                  }}
                  className="p-2 border border-gray-300 rounded text-sm italic hover:bg-gray-50"
                >
                  Italic
                </button>
              </div>
            </div>
          </Card>
        )}

        {activeTab === 'shapes' && (
          <Card className="p-4">
            <h3 className="font-medium text-gray-900 mb-3">Add Shapes</h3>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => handleAddShape('rectangle')}
                className="flex flex-col items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Square className="w-8 h-8 text-gray-600 mb-2" />
                <span className="text-sm text-gray-700">Rectangle</span>
              </button>
              <button
                onClick={() => handleAddShape('circle')}
                className="flex flex-col items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Circle className="w-8 h-8 text-gray-600 mb-2" />
                <span className="text-sm text-gray-700">Circle</span>
              </button>
            </div>
            
            {/* Color Palette */}
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Colors</h4>
              <div className="grid grid-cols-6 gap-2">
                {['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#000000'].map((color) => (
                  <button
                    key={color}
                    className="w-8 h-8 rounded border border-gray-300"
                    style={{ backgroundColor: color }}
                    onClick={() => {
                      // Add shape with selected color
                      onAddElement({
                        type: 'shape',
                        content: 'rectangle',
                        position: { x: 50, y: 50 },
                        size: { width: 100, height: 100 },
                        rotation: 0,
                        opacity: 1,
                        style: { backgroundColor: color },
                      });
                    }}
                  />
                ))}
              </div>
            </div>
          </Card>
        )}

        {activeTab === 'ai' && (
          <Card className="p-4">
            <h3 className="font-medium text-gray-900 mb-3">AI Image Generator</h3>
            <div className="space-y-3">
              <Input
                type="text"
                placeholder="Describe the image you want to create..."
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleGenerateAI()}
              />
              <Button 
                onClick={handleGenerateAI} 
                className="w-full" 
                disabled={!aiPrompt.trim() || isGenerating}
                loading={isGenerating}
              >
                {isGenerating ? 'Generating...' : 'Generate Image'}
              </Button>
            </div>
            
            {/* Example Prompts */}
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Example Prompts</h4>
              <div className="space-y-1">
                {[
                  'Minimalist mountain landscape',
                  'Cute cartoon cat with sunglasses',
                  'Geometric pattern in blue and white',
                  'Vintage coffee shop logo',
                ].map((prompt) => (
                  <button
                    key={prompt}
                    onClick={() => setAiPrompt(prompt)}
                    className="block w-full text-left text-xs text-gray-600 hover:text-primary-600 py-1"
                  >
                    "{prompt}"
                  </button>
                ))}
              </div>
            </div>
          </Card>
        )}
      </div>

      {/* Product Info */}
      <Card className="p-4 mt-6">
        <h3 className="font-medium text-gray-900 mb-2">Product Info</h3>
        <div className="text-sm text-gray-600 space-y-1">
          <p><strong>Product:</strong> {selectedProduct.name}</p>
          <p><strong>Print Area:</strong> {selectedProduct.printAreas[0]?.width}x{selectedProduct.printAreas[0]?.height}px</p>
          <p><strong>Max DPI:</strong> {selectedProduct.printAreas[0]?.maxDpi}</p>
          <p><strong>Base Price:</strong> ${selectedProduct.basePrice}</p>
        </div>
      </Card>
    </div>
  );
};

export default DesignToolbar;
