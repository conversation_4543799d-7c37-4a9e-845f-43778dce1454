import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';

import { Save, Download, Share2, ShoppingCart, ArrowLeft } from 'lucide-react';
import { Product, Design, DesignElement } from '../../types';
import { useCart } from '../../contexts/CartContext';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import DesignCanvas from '../../components/design/DesignCanvas';
import DesignToolbar from '../../components/design/DesignToolbar';
import ProductSelector from '../../components/design/ProductSelector';
import LayerPanel from '../../components/design/LayerPanel';
import toast from 'react-hot-toast';

const DesignEditorPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { addItem } = useCart();
  
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [designElements, setDesignElements] = useState<DesignElement[]>([]);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 500, height: 600 });
  const [zoom, setZoom] = useState(1);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  // Mock product data
  const mockProduct: Product = {
    id: '1',
    name: 'Classic Cotton T-Shirt',
    description: 'Premium 100% cotton t-shirt',
    category: { id: '1', name: 'T-Shirts', slug: 't-shirts' },
    basePrice: 19.99,
    images: [
      {
        id: '1',
        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop',
        alt: 'Classic Cotton T-Shirt',
        isPrimary: true,
        sortOrder: 1,
      },
    ],
    variants: [
      { id: '1', name: 'Size', type: 'size', value: 'M', priceModifier: 0, sku: 'TSHIRT-M', stock: 100, isAvailable: true },
      { id: '2', name: 'Color', type: 'color', value: 'White', priceModifier: 0, sku: 'TSHIRT-WHITE', stock: 100, isAvailable: true },
    ],
    printAreas: [
      { id: '1', name: 'Front', x: 150, y: 100, width: 200, height: 300, maxDpi: 300, allowedFileTypes: ['png', 'jpg', 'svg'] },
    ],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    tags: ['cotton', 'classic'],
  };

  useEffect(() => {
    const productId = searchParams.get('product');
    if (productId) {
      // In a real app, fetch product by ID
      setSelectedProduct(mockProduct);
    }
  }, [searchParams]);

  const handleAddElement = (element: Omit<DesignElement, 'id' | 'zIndex'>) => {
    const newElement: DesignElement = {
      ...element,
      id: `element-${Date.now()}`,
      zIndex: designElements.length,
    };
    
    setDesignElements(prev => [...prev, newElement]);
    setSelectedElement(newElement.id);
  };

  const handleUpdateElement = (elementId: string, updates: Partial<DesignElement>) => {
    setDesignElements(prev =>
      prev.map(el => el.id === elementId ? { ...el, ...updates } : el)
    );
  };

  const handleDeleteElement = (elementId: string) => {
    setDesignElements(prev => prev.filter(el => el.id !== elementId));
    if (selectedElement === elementId) {
      setSelectedElement(null);
    }
  };

  const handleSaveDesign = async () => {
    try {
      // In a real app, save to backend
      const designData = {
        productId: selectedProduct?.id,
        elements: designElements,
        createdAt: new Date().toISOString(),
      };
      
      localStorage.setItem('savedDesign', JSON.stringify(designData));
      toast.success('Design saved successfully!');
    } catch (error) {
      toast.error('Failed to save design');
    }
  };

  const handleAddToCart = () => {
    if (!selectedProduct || designElements.length === 0) {
      toast.error('Please add some design elements first');
      return;
    }

    const customDesign = {
      elements: designElements,
      printArea: selectedProduct.printAreas[0],
      previewUrl: '', // In a real app, generate preview image
    };

    addItem({
      productId: selectedProduct.id,
      product: selectedProduct,
      customDesign,
      variants: selectedProduct.variants.slice(0, 2), // Default variants
      quantity: 1,
      unitPrice: selectedProduct.basePrice,
    });

    toast.success('Added to cart!');
  };

  const handleExportDesign = () => {
    // In a real app, generate high-res export
    toast.success('Design exported successfully!');
  };

  return (
    <>
      <title>Design Editor - PrintCraft</title>
      <meta name="description" content="Create custom designs with our powerful design editor" />

      <div className="min-h-screen bg-gray-100">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  onClick={() => navigate(-1)}
                  className="mr-4"
                >
                  <ArrowLeft className="w-5 h-5 mr-2" />
                  Back
                </Button>
                <h1 className="text-xl font-semibold text-gray-900">
                  Design Editor
                  {selectedProduct && (
                    <span className="text-gray-500 ml-2">- {selectedProduct.name}</span>
                  )}
                </h1>
              </div>

              <div className="flex items-center space-x-4">
                <Button variant="outline" onClick={handleSaveDesign}>
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </Button>
                <Button variant="outline" onClick={handleExportDesign}>
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline">
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
                <Button onClick={handleAddToCart}>
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Add to Cart
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="flex h-[calc(100vh-4rem)]">
          {/* Left Sidebar - Tools */}
          <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Design Tools</h2>
            </div>
            
            <div className="flex-1 overflow-y-auto">
              {!selectedProduct ? (
                <ProductSelector onProductSelect={setSelectedProduct} />
              ) : (
                <DesignToolbar
                  onAddElement={handleAddElement}
                  selectedProduct={selectedProduct}
                />
              )}
            </div>
          </div>

          {/* Main Canvas Area */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 flex items-center justify-center p-8">
              {selectedProduct ? (
                <DesignCanvas
                  product={selectedProduct}
                  elements={designElements}
                  selectedElement={selectedElement}
                  onSelectElement={setSelectedElement}
                  onUpdateElement={handleUpdateElement}
                  zoom={zoom}
                  isPreviewMode={isPreviewMode}
                />
              ) : (
                <Card className="p-12 text-center">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Choose a Product to Start Designing
                  </h3>
                  <p className="text-gray-600">
                    Select a product from the sidebar to begin creating your custom design.
                  </p>
                </Card>
              )}
            </div>

            {/* Canvas Controls */}
            <div className="bg-white border-t border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">Zoom:</span>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setZoom(Math.max(0.25, zoom - 0.25))}
                    >
                      -
                    </Button>
                    <span className="text-sm font-medium w-12 text-center">
                      {Math.round(zoom * 100)}%
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setZoom(Math.min(3, zoom + 0.25))}
                    >
                      +
                    </Button>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <Button
                    variant={isPreviewMode ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setIsPreviewMode(!isPreviewMode)}
                  >
                    {isPreviewMode ? 'Edit Mode' : 'Preview Mode'}
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar - Layers & Properties */}
          <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Layers</h2>
            </div>
            
            <div className="flex-1 overflow-y-auto">
              <LayerPanel
                elements={designElements}
                selectedElement={selectedElement}
                onSelectElement={setSelectedElement}
                onUpdateElement={handleUpdateElement}
                onDeleteElement={handleDeleteElement}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DesignEditorPage;
